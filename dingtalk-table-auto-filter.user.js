// ==UserScript==
// @name         钉钉多维表格自动筛选
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  自动在钉钉多维表格中添加筛选条件
// <AUTHOR>
// @match        https://*.dingtalk.com/*
// @match        https://alidocs.dingtalk.com/*
// @grant        none
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';
    
    // 配置项 - 请根据实际情况修改
    const CONFIG = {
        // 您的姓名
        userName: '请填入您的姓名',
        // 需要筛选的字段名称
        filterFieldName: '负责人', // 或其他字段名
        // 等待页面加载的最大时间（毫秒）
        maxWaitTime: 10000,
        // 检查间隔（毫秒）
        checkInterval: 500
    };
    
    // 等待元素出现的工具函数
    function waitForElement(selector, timeout = CONFIG.maxWaitTime) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            
            function check() {
                const element = document.querySelector(selector);
                if (element) {
                    resolve(element);
                    return;
                }
                
                if (Date.now() - startTime > timeout) {
                    reject(new Error(`Element ${selector} not found within ${timeout}ms`));
                    return;
                }
                
                setTimeout(check, CONFIG.checkInterval);
            }
            
            check();
        });
    }
    
    // 等待多个可能的选择器中任一个出现
    function waitForAnyElement(selectors, timeout = CONFIG.maxWaitTime) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            
            function check() {
                for (const selector of selectors) {
                    const element = document.querySelector(selector);
                    if (element) {
                        resolve({ element, selector });
                        return;
                    }
                }
                
                if (Date.now() - startTime > timeout) {
                    reject(new Error(`None of the elements found within ${timeout}ms`));
                    return;
                }
                
                setTimeout(check, CONFIG.checkInterval);
            }
            
            check();
        });
    }
    
    // 模拟点击事件
    function simulateClick(element) {
        const events = ['mousedown', 'mouseup', 'click'];
        events.forEach(eventType => {
            const event = new MouseEvent(eventType, {
                view: window,
                bubbles: true,
                cancelable: true
            });
            element.dispatchEvent(event);
        });
    }
    
    // 模拟输入事件
    function simulateInput(element, value) {
        element.focus();
        element.value = value;
        
        // 触发输入事件
        const events = ['input', 'change', 'blur'];
        events.forEach(eventType => {
            const event = new Event(eventType, {
                bubbles: true,
                cancelable: true
            });
            element.dispatchEvent(event);
        });
    }
    
    // 主要的自动筛选函数
    async function autoFilter() {
        try {
            console.log('开始自动筛选...');
            
            // 等待页面加载完成
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // 常见的筛选按钮选择器（需要根据实际页面调整）
            const filterButtonSelectors = [
                '[data-testid="filter-button"]',
                '.filter-button',
                '[title*="筛选"]',
                '[aria-label*="筛选"]',
                'button[class*="filter"]',
                '.toolbar button:contains("筛选")',
                // 添加更多可能的选择器
            ];
            
            // 查找筛选按钮
            let filterButton;
            try {
                const result = await waitForAnyElement(filterButtonSelectors);
                filterButton = result.element;
                console.log('找到筛选按钮:', result.selector);
            } catch (error) {
                console.log('未找到筛选按钮，尝试其他方法...');
                // 可以在这里添加其他查找筛选功能的方法
                return;
            }
            
            // 点击筛选按钮
            simulateClick(filterButton);
            console.log('已点击筛选按钮');
            
            // 等待筛选面板出现
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 查找字段选择器或输入框
            const inputSelectors = [
                'input[placeholder*="请输入"]',
                'input[placeholder*="筛选"]',
                '.filter-input input',
                '.search-input input',
                '[data-testid="filter-input"]'
            ];
            
            let inputElement;
            try {
                const result = await waitForAnyElement(inputSelectors);
                inputElement = result.element;
                console.log('找到输入框:', result.selector);
            } catch (error) {
                console.log('未找到输入框');
                return;
            }
            
            // 输入筛选条件
            simulateInput(inputElement, CONFIG.userName);
            console.log('已输入筛选条件:', CONFIG.userName);
            
            // 等待一下，然后查找确认按钮
            await new Promise(resolve => setTimeout(resolve, 500));
            
            const confirmButtonSelectors = [
                'button:contains("确定")',
                'button:contains("确认")',
                'button:contains("应用")',
                '[data-testid="confirm-button"]',
                '.confirm-button',
                'button[class*="confirm"]'
            ];
            
            // 查找并点击确认按钮
            for (const selector of confirmButtonSelectors) {
                const confirmButton = document.querySelector(selector);
                if (confirmButton) {
                    simulateClick(confirmButton);
                    console.log('已点击确认按钮');
                    break;
                }
            }
            
            console.log('自动筛选完成');
            
        } catch (error) {
            console.error('自动筛选失败:', error);
        }
    }
    
    // 检查是否是多维表格页面
    function isTablePage() {
        const url = window.location.href;
        // 根据实际的钉钉多维表格URL模式调整
        return url.includes('dingtalk.com') && 
               (url.includes('table') || url.includes('sheet') || url.includes('alidocs'));
    }
    
    // 页面加载完成后执行
    function init() {
        if (!isTablePage()) {
            return;
        }
        
        console.log('检测到钉钉多维表格页面，准备自动筛选...');
        
        // 延迟执行，确保页面完全加载
        setTimeout(() => {
            autoFilter();
        }, 3000);
    }
    
    // 监听页面变化（适用于单页应用）
    let lastUrl = location.href;
    new MutationObserver(() => {
        const url = location.href;
        if (url !== lastUrl) {
            lastUrl = url;
            setTimeout(init, 2000);
        }
    }).observe(document, { subtree: true, childList: true });
    
    // 初始化
    init();
    
})();
